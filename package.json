{"name": "updraft-contracts", "version": "1.0.0", "main": "hardhat.config.ts", "repository": "https://github.com/UpdraftFund/contracts", "author": "adams<PERSON><PERSON> <205792+adamstal<PERSON>@users.noreply.github.com>", "license": "MIT", "private": true, "devDependencies": {"@nomicfoundation/hardhat-ignition": "^0.15.0", "@nomicfoundation/hardhat-ignition-viem": "^0.15.0", "@nomicfoundation/hardhat-network-helpers": "^1.0.0", "@nomicfoundation/hardhat-toolbox-viem": "^3.0.0", "@nomicfoundation/hardhat-verify": "^2.0.14", "@nomicfoundation/hardhat-viem": "^2.0.0", "@truffle/dashboard-hardhat-plugin": "^0.2.15", "@types/chai": "^4.2.0", "@types/chai-as-promised": "^7.1.6", "@types/mocha": ">=9.1.0", "@types/node": ">=18.0.0", "chai": "^4.2.0", "hardhat": "^2.26.1", "hardhat-gas-reporter": "^1.0.8", "solidity-coverage": "^0.8.0", "ts-node": ">=8.0.0", "typescript": "~5.0.4", "viem": "^2.7.6"}, "dependencies": {"@openzeppelin/contracts": "^5.1.0"}}